import concurrent.futures
import logging
import os
import shutil
import sys
import time

from netmiko import ConnectHandler


# 创建文件目录
def create_dir() -> bool:
    # 创建文件夹的名称。格式：YYYYmmdd巡检文件，存放命令回显数据
    file_name = time.strftime("%Y%m%d", time.localtime()) + "楼层巡检文件"
    file_directory = os.path.dirname(os.path.abspath(__file__))
    # 判断是否存在，如果存在，则删除并创建，否则直接创建文件夹
    try:
        if os.path.exists(file_directory + "\\" + file_name):
            shutil.rmtree(file_directory + "\\" + file_name, ignore_errors=True)
            os.makedirs(file_directory + "\\" + file_name)
        else:
            os.makedirs(file_directory + "\\" + file_name)
        return True
    except:
        return False


# 创建线程池，定义方法
def getinfobyssh(name, value, order):
    global config_dic
    global file_directory
    global file_name
    # 创建文件夹
    os.makedirs(file_directory + "\\" + file_name + "\\" + name + "-" + time.strftime("%Y%m%d", time.localtime()))
    with ConnectHandler(**value, timeout=15) as conn:
        for item in order:
            # 根据命令创建文件以及文件夹
            txt_name = file_directory + "\\" + file_name + "\\" + name + "-" + time.strftime("%Y%m%d",
                                                                                             time.localtime()) + "\\" + ''.join(
                item) + '.txt'
            print(f"{name}文件夹创建完毕，开始执行{item}命令")
            for value in item:
                output = conn.send_command_timing(value)
                with open(txt_name, mode='a', encoding='utf8') as f:
                    f.write("命令：" + value + "\n")
                    f.write(value + "命令开始\n")
                    f.write(output)
                    f.write(value + "命令结束\n")
    return name


if __name__ == '__main__':
    # 初始变量
    information = []
    config_dic = dict()

    if create_dir() is False:
        logging.error("创建文件出错！")
        sys.exit()

    # 先写死，后面采用读取方式进行
    order = [["dis cu", "dis int desc"], ["dis vlan", "dis mac-add"],
             ["dis ip routing-table", "dis arp all", "dis ip routing-table statistics"],
             ["dis cpu", "dis memory", "dis device", "dis power", "dis fan", "dis temperature all"],
             ["dis version", "dis transceiver diagnosis interface", "dis eth-trunk"]]

    with open('config1.txt', mode='r', encoding='utf8') as f:
        for line in f.read().splitlines():
            information.append(line)
    # 创建字典
    for item in information:
        new_item = item.split()
        son_dic = {'device_type': new_item[1],
                   'host': new_item[2],
                   'port': new_item[3],
                   'username': new_item[4],
                   'password': new_item[5],
                   'conn_timeout': 15}
        config_dic[new_item[0]] = son_dic

    file_directory = os.path.dirname(os.path.abspath(__file__))
    file_name = time.strftime("%Y%m%d", time.localtime()) + "楼层巡检文件"
    # 创建线程池，用于发送指定命令并获得回显
    future_to_key = {}
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        for key, value in config_dic.items():
            future = executor.submit(getinfobyssh, key, value, order)
            future_to_key[future] = key
        for future in concurrent.futures.as_completed(future_to_key):
            arg = future_to_key[future]
            try:
                result = future.result()
                print(f"{arg}对应的result:{result}")
            except Exception as e:
                print(f"{arg}对应的result:{result}")
                print(f"任务执行失败，参数：{arg}, 出现了错误：{e}")

    # executor = ThreadPoolExecutor(max_workers=9)
    # i = 1
    # for result in executor.map(getinfobyssh,
    #                            ["HW-SW-5731-core", "HW-SW-5735-Aggregation-2", "HW-SW-5730S-Aggregation-3",
    #                             "HW-SW-5730S-Aggregation-4", "HW-SW-5735-Aggregation-5", "HW-SW-5735-access-5",
    #                             "HW-SW-5735-access-6", "HW-SW-5731-multifunction1-6", "HW-SW-5731-multifunction2-6"]):
    #     print("task{}:{}".format(i, result))
    #     i += 1
    # # 遍历数组元素，并将元素以空格进行分割得到数组，这个数组首个元素作为字典的键，其余元素作为新字典的值。
    # # 提前打开3cdftp服务器软件，后期再使用python构建ftp服务器
