import logging
import os
import shutil
import sys
import time
from concurrent.futures import ThreadPoolExecutor
from pickle import GLOBAL

from netmiko import ConnectHandler

# 创建文件目录
def create_dir(file_name, file_directory) -> bool:
    # 判断是否存在，如果存在，则删除并创建，否则直接创建文件夹
    try:
        if os.path.exists(file_directory + "\\" + file_name):
            shutil.rmtree(file_directory + "\\" + file_name, ignore_errors=True)
            os.makedirs(file_directory + "\\" + file_name)
        else:
            os.makedirs(file_directory + "\\" + file_name)
        return True
    except:
        return False


# 创建线程池，定义方法
def getinfobyssh(name):
    global config_dic
    global file_directory
    global file_name
    # 创建文件夹
    os.makedirs(file_directory + "\\" + file_name + "\\" + name + "-" + time.strftime("%Y%m%d", time.localtime()))
    with ConnectHandler(**config_dic[name]) as conn:
        # 根据品牌选着命令，默认华三
        if config_dic[name]['device_type'] == 'hillstone_stoneos':
            order = hillorder
        elif config_dic[name]['device_type'].startswith('huawei'):
            order = hworder
            conn.send_command('screen-length 0 temporary')
        elif config_dic[name]['device_type'] == 'ruijie_os':
            order = rgorder
            conn.send_command('terminal length 0')
        else:
            order = h3corder
            conn.send_command('screen-length disable')


        for item in order:

            # 根据命令创建文件以及文件夹
            txt_name = file_directory + "\\" + file_name + "\\" + name + "-" + time.strftime("%Y%m%d",
                                                                                             time.localtime()) + "\\" + ''.join(
                item) + '.txt'

            for value in item:
                output = conn.send_command_timing(value,read_timeout=60)

                # 检查输出是否包含分页提示（多种格式）
                import re
                page_patterns = [
                    r'---- More ----$',
                    r'-- More --$',
                    r'\(more\)$',
                    r'\(continues\)$',
                    r'More\.\.\.$'
                ]

                has_more = any(re.search(pattern, output.strip()) for pattern in page_patterns)

                if has_more:
                    full_output = output
                    while True:
                        # 发送空格继续
                        conn.write_channel(' ')
                        # 等待设备响应
                        time.sleep(0.1)
                        # 读取更多输出
                        more_output = conn.read_channel()
                        full_output += more_output

                        # 检查是否还有分页提示
                        has_more_again = any(re.search(pattern, more_output.strip()) for pattern in page_patterns)
                        if not has_more_again:
                            break
                    full_output = full_output.replace('---- More ----', '').strip()
                    output = full_output

                with open(txt_name, mode='a', encoding='utf8') as f:
                    f.write("命令：" + value + "\n")
                    f.write(value + "命令开始\n")
                    f.write(output)
                    f.write(value + "命令结束\n")
    return name


# 暂时没有实现
def uploadbyftp(name):
    # 这个方法传入设备的名称，然后通过配置信息，ssh连接，使用ftp协议上传命令
    global config_dic
    with ConnectHandler(**config_dic[name]) as conn:
        # 根据不同的设备，读取不同的命令数组order命令，然后根据回显结尾来判断是否成功
        for item in order:
            output = conn.send_command(item)
    return name


if __name__ == '__main__':
    # 初始变量
    information = []
    config_dic = dict()
    # 创建文件夹的名称。格式：YYYYmmdd巡检文件，存放命令回显数据
    file_name = time.strftime("%Y%m%d", time.localtime()) + "巡检文件"
    file_directory = os.path.dirname(os.path.abspath(__file__))
    if create_dir(file_name, file_directory) is False:
        logging.error("创建文件出错！")
        sys.exit()

    # 先写死，后面采用读取方式进行
    #H3C
    order = [["dis cu", "dis int brief"], ["dis vlan brief", "dis mac-ad"],
             ["dis ip routing-table", "dis arp all", "dis ip routing-table statistics"],
             ["dis cpu", "dis memory", "dis device", "dis power", "dis fan", "dis env"],
             ["dis version", "dis tran dia int", "dis link-agg verb"]]
    h3corder = order
    #ruijie
    rgorder = [["show run","show interface status"],["show vlan", "show mac-address-table "],
               ["show ip route ", "show arp ", "show ip route summary"],
               ["show cpu", "show memory", "show power", "show device-fr", "show temperature"],
               ["show version", "show interfaces transceiver", "show aggregatePort summary"]]
    #huawei
    hworder = [["dis cu", "dis int desc"], ["dis vlan", "dis mac-add"],
             ["dis ip routing-table", "dis arp all", "dis ip routing-table statistics"],
             ["dis cpu", "dis memory", "dis device", "dis power", "dis fan", "dis temperature all"],
             ["dis version", "dis transceiver diagnosis interface", "dis eth-trunk"]]
    #hillstone
    hillorder = [["show configuration","show interface"],["show vlan", "show mac"],
               ["show ip route ", "show arp ", "show ip route summary"],
               ["show cpu", "show memory", "show power", "show module", "show environment"],
               ["show version", "show transceiver", "show lacp"]]
    with open('config.txt', mode='r', encoding='utf8') as f:
        for line in f.read().splitlines():
            information.append(line)
    # 创建字典
    for item in information:
        new_item = item.split()
        son_dic = {'device_type': new_item[1],
                   'host': new_item[2],
                   'port': new_item[3],
                   'username': new_item[4],
                   'password': new_item[5]
                   }
        config_dic[new_item[0]] = son_dic

    # 创建线程池，用于发送指定命令并获得回显
    executor = ThreadPoolExecutor(max_workers=10)
    i = 1
    #for result in executor.map(getinfobyssh, ["SW-H3CS6800", "SW-H3CS7506", "SW-H3CS10508", "FW-H3CF5030"]):
    for result in executor.map(getinfobyssh, ["DJG-SW-H3C-V10508-核心交换机","DJG-FW-shanshi-X7180-出口防火墙(A)","DJG-FW-shanshi-X7180-出口防火墙(B)","DJG-SW-RG-S6200-服务器域-fabu1","DJG-SW-RG-S5750-服务器域-fabu2"
        ,"DJG-AC-HW-6005-无线控制器","DJG-FW-HW-S5735-馆内6楼-汇聚层","DJG-SW-HW-5735-馆内6楼-多功能1","DJG-SW-HW-5735-馆内6楼-多功能2","DJG-SW-H3C_S6800-出口交换机（透传）","DJG-SW-H3C_S7506X-总汇聚交换机",
        "DJG-SW-HW-S5731-馆内楼层-核心交换机","DJG-SW-HW-S5735-馆内2楼-汇聚层","DJG-SW-HW-S5735-馆内3楼-汇聚层","DJG-SW-HW-S5735-馆内4楼-汇聚层","DJG-SW-HW-S5735-馆内5楼-接入层","DJG-SW-HW-S5735-馆内5楼-汇聚层","DJG-SW-RG-S5750-服务器域-tuoguan1","DJG-SW-RG-S6200-服务器域-H3Ccloud2yewu"]):
        print("task{}:{}".format(i, result))
        i += 1
    # 遍历数组元素，并将元素以空格进行分割得到数组，这个数组首个元素作为字典的键，其余元素作为新字典的值。
    # 提前打开3cdftp服务器软件，后期再使用python构建ftp服务器
