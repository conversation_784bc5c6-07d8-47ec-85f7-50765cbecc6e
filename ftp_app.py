from datetime import datetime
import os
import shutil
import signal
from pathlib import Path

from pyftpdlib.authorizers import DummyAuthorizer
from pyftpdlib.handlers import FTPHandler
from pyftpdlib.servers import FTPServer

from config.constants import FTP_USERNAME, FTP_PASSWORD


class FTP_SERVER():
    def __init__(self, username: str, password: str, path: str, ip_address: str, perm: str):
        self.username = username
        self.password = password
        self.path = path
        self.ip_address = ip_address
        self.perm = perm
    def start(self):
        # 创建一个虚拟用户并设置权限
        authorizer = DummyAuthorizer()
        print(f"当前登录的用户是：{self.username}")
        authorizer.add_user(self.username, self.password, self.path,
                            perm=self.perm)
        # 初始化 FTP 处理器并应用授权器
        handler = FTPHandler
        handler.authorizer = authorizer
        # 创建 FTP 服务器实例
        server = FTPServer((self.ip_address, 21), handler)
        def signal_handler(sig, frame):
            print("Received signal to stop the server. Shutting down gracefully.")
            server.close_all()
            # 注册信号处理程序
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        server.serve_forever()
if __name__ == "__main__":
    # 文件目录结构 path -> file202311 -> 自行让程序创建
    current_time = datetime.now()
    # 将时间转换为字符串
    time_string = current_time.strftime("%Y%m%d")
    print(current_time.strftime("%Y%m%d"))
    # path = str(Path(__file__).resolve().parent) + r"\path\file"+time_string
    path = "\\path\\file"+time_string
    file_directory = os.path.dirname(os.path.abspath(__file__))+"\\" + path
    if os.path.exists(file_directory):
        shutil.rmtree(file_directory, ignore_errors=True)
        os.makedirs(file_directory)
    else:
        os.makedirs(file_directory)
    print(f"ftp服务端的文件目录{file_directory}")
    ftp_server = FTP_SERVER(FTP_USERNAME, FTP_PASSWORD, file_directory, "10.40.251.236", "elradfmw")
    ftp_server.start()
