import concurrent.futures.thread
import re
from pathlib import Path

from netmiko import Connect<PERSON>andler

from config.constants import FTP_PASSWORD, FTP_USERNAME, SERVER_IP


def get_file_name(info: str):
    # file_name = []
    # 这里获取bin结尾、时间字符串开头的
    file_name = re.findall(r"(?<=[ 0-9:]{12})[a-zA-Z0-9\-]*.bin", info)
    # 然后再找cfg文件
    file_name.extend(re.findall(r"(?<=[ 0-9:]{12})[a-zA-Z0-9\-]*.cfg", info))
    return file_name


def get_file(server_ip: str, user: str, password: str, device_name: str, machine: dict):
    path = str(Path(__file__).resolve().parent) + r"/a.log"
    with ConnectHandler(**machine, session_log=path) as conn:
        # 第一步，先获取网络设备的目录
        # 第二步，读取信息并处理数据
        # 第三步，读取传进来的参数，进行命令处理
        output = conn.send_command_timing(command_string='dir')
        file_name = get_file_name(output)
        output = output + conn.send_command_timing(command_string="ftp " + server_ip)
        output = output + conn.send_command_timing(command_string=user)
        output = output + conn.send_command_timing(command_string=password)
        # 这里是进入path目录后的file202311，然后根据传进来的参数，创建目录
        output = output + conn.send_command_timing(command_string="mkdir " + device_name)
        output = output + conn.send_command_timing(command_string="cd " + device_name)

        print(f"设备：{device_name},完成初始化，开始文件上传.....")
        print(f"当前设备需要上传的文件是：{file_name}")
        # 循环备份核心文件
        for name in file_name:
            output = output + conn.send_command_timing(command_string='put ' + name, read_timeout=240)
            print(f"设备{device_name}。完成了{name}的文件上传...")


if __name__ == '__main__':

    # 创建线程池，传入字典参数
    # 创建字典，存放设备信息，键为设备名称，值为设备的信息字典
    machines = {
        # "SW-H3CS6800": {'device_type': 'hp_comware',
        #                 'host': '***********',
        #                 'port': 822,
        #                 'username': 'tanjuntao',
        #                 'password': '!tjT&99&LBL',
        #                 },
        # "SW-H3CS7506": {'device_type': 'hp_comware',
        #                 'host': '***********',
        #                 'port': 822,
        #                 'username': 'tanjuntao',
        #                 'password': '!tjT&99&LBL',
        #                 },
        "SW-H3CS10508": {'device_type': 'hp_comware',
                         'host': '***********',
                         'port': 822,
                         'username': 'chenjiahao',
                         'password': 'chenjiahao@2025',
                          }
        #,
        # "FW-H3CF5030": {'device_type': 'hp_comware',
        #                 'host': '**********',
        #                 'port': 22,
        #                 'username': 'tanjuntao',
        #                 'password': '$tjT%99X5030',
        #                 }
    }
    # 我想在这里连接机器并且使用netmiko获取东西
    # 创建连接参数
    future_dict = {}
    with concurrent.futures.thread.ThreadPoolExecutor(max_workers=6) as executor:
        # 遍历得到字典
        for key, value in machines.items():
            print(key)
            future = executor.submit(get_file, SERVER_IP, FTP_USERNAME, FTP_PASSWORD, key, value)
            # 将future映射为key
            future_dict[future] = key

        for future in concurrent.futures.as_completed(future_dict):
            # 获取已经完成的future
            # 获取future中的参数
            key = future_dict[future]
            try:
                # 使用result查看完成的future是否有异常
                result = future.result()
                print(f"{key}对应的result:{result}")
                # 处理结果
            except Exception as e:
                # 捕捉异常，然后进行判断
                print(f"任务执行失败，参数：{key}, 出现了错误：{e}")
                # 如果需要重试，将任务重新加入队列
