import multiprocessing
import subprocess
from pathlib import Path

# 设定全局常量
VENV_PYTHON_PATH = str(Path(__file__).resolve().parent) + r"\venv\Scripts\python.exe"

print(VENV_PYTHON_PATH)
def run_script(python_path,script_name):
    """运行一个独立的 Python 脚本"""
    subprocess.run([python_path, script_name])

if __name__ == '__main__':
    # 创建一个进程执行网络设备的巡检
    p1 = multiprocessing.Process(target=run_script, args=(VENV_PYTHON_PATH,"app.py",))
    p1.start()
    # 创建一个进行执行楼层设备的巡检
    p2 = multiprocessing.Process(target=run_script, args=(VENV_PYTHON_PATH,"louceng.py",))
    p2.start()
    # 创建一个进程，充当ftp的服务进程


    # 等待第一个进程结束后，执行ftp的备份进程
    p1.join()
    p3 = multiprocessing.Process(target=run_script, args=(VENV_PYTHON_PATH,"ftp_app.py",))
    p4 = multiprocessing.Process(target=run_script, args=(VENV_PYTHON_PATH,"test.py",))
    p3.start()
    p4.start()
    p4.join()
    p2.join()
    p3.terminate()
    p3.join()
